'use client'
import { Divider } from '@/components/divider.jsx'
import { Select } from '@/components/select.jsx'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/table.jsx'
import { Stat } from '@/customComponents/common/stat.jsx'
import { customerStatementAtom, retailerAtom } from '@/customComponents/providers.tsx'
import { getFinancialYearStartDate } from '@/utils/date'
import { convertToNumber, indianCurrencyFormat } from '@/utils/formats'
import { useFetchAccountStatementByCustomerId } from '@/utils/requests'
import { useAtomValue } from 'jotai'
import moment from 'moment'
import { useEffect, useState } from 'react'

const dateFormat = 'DD MMM YYYY'

const buttonList = [
  { label: 'FY (24-25)', value: 'thisFynYear' },
  { label: 'Current Month', value: 'currentMonth' },
  { label: 'Last Month', value: 'lastMonth' },
  { label: 'This Quarter', value: 'thisQuarter' },
]

const AccountStatements = () => {
  const customer = useAtomValue(retailerAtom)
  useFetchAccountStatementByCustomerId(customer.customerId)
  const customerStatement = useAtomValue(customerStatementAtom)
  const [selectedStatement, setSelectedStatement] = useState([])
  const [startDate, setStartDate] = useState(moment(getFinancialYearStartDate()).format(dateFormat))
  const [endDate, setEndDate] = useState(moment().format(dateFormat))

  useEffect(() => {
    if (customerStatement.length > 0) {
      setSelectedStatement(customerStatement)
    }
  }, [customerStatement])

  /*   const getPreviousStatement = (startDate) => {
    const formattedStartDate = moment(startDate, dateFormat)
    if (!formattedStartDate.isValid()) {
      throw new Error('Invalid startDate or dateFormat')
    }

    let previousStatement = [...customerStatement].reverse().find((statement) => {
      const statementDate = moment(statement.txnDate)
      return statementDate.isBefore(formattedStartDate)
    })

    if (!previousStatement) {
      console.warn('No matching previous statement found')
      return null
    }

    return previousStatement
  }

  const getStatementBetween = (startDate, endDate) => {
    let previousStatement = customerStatement.find((statement) => {
      const statementDate = moment(statement.txnDate)
      return statementDate.isBetween(moment(startDate, dateFormat), moment(endDate, dateFormat), null, [])
    })
    return previousStatement
  }
 */
  const onFilterChange = (e) => {
    if (Array.isArray(customerStatement) && customerStatement.length > 0) {
      const filter = e.target.value
      let newStartDate, newEndDate

      switch (filter) {
        case 'thisFynYear':
          newStartDate = moment().month(3).startOf('month').format(dateFormat)
          newEndDate = moment().add(1, 'year').month(2).endOf('month').format(dateFormat)
          break
        case 'currentMonth':
          newStartDate = moment().startOf('month').format(dateFormat)
          newEndDate = moment().endOf('month').format(dateFormat)
          break
        case 'lastMonth':
          newStartDate = moment().subtract(1, 'month').startOf('month').format(dateFormat)
          newEndDate = moment().subtract(1, 'month').endOf('month').format(dateFormat)
          break
        case 'thisQuarter':
          newStartDate = moment().startOf('quarter').format(dateFormat)
          newEndDate = moment().endOf('quarter').format(dateFormat)
          break
        default:
          newStartDate = null
          newEndDate = null
      }

      setStartDate(newStartDate)
      setEndDate(newEndDate)

      // Filter statements based on date range
      if (newStartDate && newEndDate && filter !== 'thisFynYear') {
        const filteredStatements = customerStatement?.slice(1)?.filter((statement) => {
          const statementDate = moment(statement.txnDate)
          return statementDate.isBetween(moment(newStartDate, dateFormat), moment(newEndDate, dateFormat), 'day', '[]')
        })

        if (Array.isArray(filteredStatements) && filteredStatements.length > 0) {
          setSelectedStatement([
            {
              txnDate: newStartDate,
              vchType: 'Opening Balance',
              invoiceNumber: '',
              particulars: '***Opening Balance***',
              debit: 0,
              credit: 0,

              balance: filteredStatements[0].balance + filteredStatements[0].credit - filteredStatements[0].debit,
            },
            ...filteredStatements,
          ])

        } else {
          const lastStatement = customerStatement[customerStatement.length - 1]
          setSelectedStatement([
            {
              txnDate: newStartDate,
              vchType: 'Opening Balance',
              invoiceNumber: '',
              particulars: '***Opening Balance***',
              debit: 0,
              credit: 0,

              balance: lastStatement.balance,
            },
          ])
        }
      } else {
        setSelectedStatement(customerStatement)
      }
    }
  }

  return (
    <>
      <div className="pb-6 mt-2 align-baseline sm:flex sm:justify-between">
        <div className="flex flex-col">
          <span className="text-2xl font-bold">Account Statement</span>
          <span className="text-sm text-gray-400">
            {startDate} to {endDate}
          </span>
        </div>
      </div>
      <div className="flex flex-col justify-between lg:flex-row">
        <div className="w-1/2 sm:mt-0 sm:w-auto lg:w-48">
          <Select onChange={onFilterChange} name="accountStatement">
            {buttonList.map(({ label, value }) => (
              <option key={value} value={value}>
                {label}
              </option>
            ))}
          </Select>
        </div>
      </div>
      <Table className="mt-2 hidden justify-start text-left [--gutter:theme(spacing.6)] md:block lg:mt-1 lg:[--gutter:theme(spacing.10)]">
        <TableHead>
          <TableRow>
            <TableHeader>Date</TableHeader>
            <TableHeader>Vch Type / Number</TableHeader>
            <TableHeader className="w-full">Particulars</TableHeader>
            <TableHeader className="text-right">Debit</TableHeader>
            <TableHeader className="text-right">Credit</TableHeader>
            <TableHeader className="text-right">Balance</TableHeader>
          </TableRow>
        </TableHead>
        <TableBody>
          {selectedStatement.length > 0 &&
            selectedStatement?.map((transaction, index) => {
              return (
                <TableRow key={index + 2} className="justify-start text-start">
                  <TableCell className="h-20 text-start">
                    <div className="h-full">{moment(transaction.txnDate).format('DD MMM YYYY')}</div>
                  </TableCell>
                  <TableCell className="h-20 justify-start text-left">
                    <div className="h-full">
                      {transaction?.vchType} <br /> {transaction.invoiceNumber}
                    </div>
                  </TableCell>
                  <TableCell className="h-20 text-pretty text-sm leading-tight">
                    <div className="h-full">{transaction.particulars}</div>
                  </TableCell>
                  <TableCell className="h-20 text-right">
                    <div className="h-full">{indianCurrencyFormat(convertToNumber(transaction.debit))} </div>
                  </TableCell>
                  <TableCell className="h-20 text-right">
                    <div className="h-full"> {indianCurrencyFormat(convertToNumber(transaction.credit))}</div>
                  </TableCell>
                  <TableCell className="h-20 text-right">
                    <div className="h-full">{indianCurrencyFormat(transaction?.balance?.toFixed(2))}</div>
                  </TableCell>
                </TableRow>
              )
            })}
        </TableBody>
      </Table>

      {selectedStatement.length > 0 &&
        selectedStatement?.map((transaction, index) => {
          return (
            <div key={index + 1} className="my-2 flex flex-col rounded-lg border p-3 md:hidden">
              <div className="flex flex-col items-start">
                <div className="flex w-full justify-between">
                  <span className="text-[.6rem]">
                    {moment(transaction.txnDate).format('DD MMM YYYY')} &#183; {transaction.invoiceNumber}
                  </span>

                  <span className="rounded-sm bg-slate-100 px-1 text-[.6rem] capitalize">
                    {transaction?.vchType?.toUpperCase()}
                  </span>
                </div>
                <Divider className="mt-1" />
                <div className="mt-1 flex w-full flex-row justify-between">
                  <Stat
                    key={index}
                    title={'Debit'}
                    value={indianCurrencyFormat(convertToNumber(transaction.debit))}
                    index={index}
                    change="+4.5%"
                  />
                  <Stat
                    key={index}
                    title={'Credit'}
                    value={indianCurrencyFormat(convertToNumber(transaction.credit))}
                    index={index}
                    change="+4.5%"
                  />
                  <Stat
                    key={index}
                    title={'Balance'}
                    value={indianCurrencyFormat(convertToNumber(transaction.balance))}
                    index={index}
                    change="+4.5%"
                  />
                </div>
              </div>
              <div className="mt-2 text-[11px] leading-tight text-gray-400">{transaction.particulars}</div>
            </div>
          )
        })}
    </>
  )
}

export default AccountStatements
