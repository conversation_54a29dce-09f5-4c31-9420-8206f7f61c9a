'use client';

import { atom } from 'jotai';
import { atomWithStorage, createJSONStorage } from 'jotai/utils';
import { User } from 'firebase/auth';
import { RecaptchaVerifier, ConfirmationResult } from 'firebase/auth';

// Define types for your atoms
export type PriceListType = Record<string, any>;
export type RetailerType = Record<string, any>;
export type CustomerStatementType = Record<string, any>;

// Create custom storage with SSR check
const storage = createJSONStorage<any>(() => {
  // Check if window is defined (client-side)
  if (typeof window !== 'undefined') {
    return window.localStorage;
  }
  // Return dummy storage for server-side
  return {
    getItem: () => null,
    setItem: () => {},
    removeItem: () => {},
  };
});

// Atoms for data storage
export const priceListAtom = atomWithStorage<PriceListType>('priceList', {}, storage);
export const retailerAtom = atomWithStorage<RetailerType>('retailer', {}, storage);
export const customerStatementAtom = atomWithStorage<CustomerStatementType>('customerStatement', {}, storage);
export const dashboardAtom = atomWithStorage('dashboard', {}, storage);
export const duesAtom = atomWithStorage('dues', {}, storage);
export const invoicesAtom = atomWithStorage('invoices', {}, storage);
export const salesOrdersAtom = atomWithStorage('salesOrders', {}, storage);
export const productCatalogueAtom = atomWithStorage('productCatalogue', {}, storage);
export const carouselDataAtom = atomWithStorage('carouselData', {}, storage);
export const schemeDataAtom = atomWithStorage('schemeData', {}, storage);
export const salesMasterDataAtom = atomWithStorage('salesMasterData', {}, storage);
export const smrReportAtom = atomWithStorage('smrReport', {}, storage);
export const supportPersionsAtom = atomWithStorage('supportPersions', {}, storage);
export const farmerVisitsAtom = atomWithStorage('farmerVisits', {}, storage);
export const customerPaymentsAtom = atomWithStorage('customerPayments', {}, storage);

// Atoms for Firebase instances (do not persist in local storage)
export const recaptchaVerifierAtom = atom<RecaptchaVerifier | null>(null);
export const confirmationResultAtom = atom<ConfirmationResult | null>(null);
export const firebaseUserAtom = atom<User | null>(null);
export const loadingAtom = atom<boolean>(true);
export const errorAtom = atomWithStorage<Record<string, any>>('errorMessage', {});
export const selectedTabAtom = atom(0);

// Provider component
export function Providers({ children }: { children: React.ReactNode }) {
  return <>{children}</>;
}
