{"timestamp": "2025-06-14T06:38:14.283Z", "duration": 6125, "summary": {"total": 7, "passed": 6, "failed": 1}, "results": {"environmentVariables": {"success": true, "data": {"status": "healthy", "message": "All required environment variables are set", "required_variables": ["ZOHO_PAY_ACCOUNT_ID", "MONGODB_URI"], "missing_variables": []}}, "healthCheck": {"success": true, "data": {"timestamp": "2025-06-14T06:38:11.325Z", "service": "Zoho Payment Integration", "version": "1.0.0", "status": "healthy", "checks": {"database": {"status": "healthy", "message": "Database connection successful"}, "environment": {"status": "healthy", "message": "All required environment variables are set", "required_variables": ["ZOHO_PAY_ACCOUNT_ID", "MONGODB_URI"], "missing_variables": []}, "zoho_auth": {"status": "healthy", "message": "Zoho authentication token is available", "has_token": true, "token_source": "external_service"}, "zoho_api": {"status": "healthy", "message": "Zoho API is accessible"}}, "summary": {"total_checks": 4, "healthy_checks": 4, "unhealthy_checks": 0}, "configuration": {"account_id": "configured", "webhook_secret": "configured", "domain": "https://partner.aquaconnect.blue"}}}, "databaseConnection": {"success": true, "status": "healthy"}, "legacyInitiatePayment": {"success": true, "data": {"result": "success", "paymentSession": {"payments_session_id": "****************", "currency": "INR", "amount": "1000.50", "description": "Payment for Invoice TEST-INV-001", "invoice_number": "TEST-INV-001", "created_time": **********, "meta_data": [{"key": "legacy_endpoint", "value": "true"}, {"key": "invoice_no", "value": "TEST-INV-001"}, {"key": "customer_id", "value": "TEST-CUST-001"}, {"key": "invoice_number", "value": "TEST-INV-001"}]}, "transaction_id": "684d18d5ed5e2e2b30c85270", "payment_session_id": "****************", "expires_in": "15 minutes", "paymentSessionId": "****************"}}, "newPaymentSessionAPI": {"success": true, "data": {"success": true, "message": "Payment session created successfully", "data": {"payment_session_id": "****************", "amount": "1000.50", "currency": "INR", "description": "Test payment for aquaculture products", "invoice_number": "TEST-INV-001", "created_time": **********, "transaction_id": "684d18d5ed5e2e2b30c85277", "expires_in": "15 minutes"}, "payment_session": {"payments_session_id": "****************", "currency": "INR", "amount": "1000.50", "description": "Test payment for aquaculture products", "invoice_number": "TEST-INV-001", "created_time": **********, "meta_data": [{"key": "product_type", "value": "aquaculture"}, {"key": "customer_id", "value": "TEST-CUST-001"}, {"key": "invoice_number", "value": "TEST-INV-001"}, {"key": "order_id", "value": "ORD-TEST-001"}]}}}, "zohoPaymentGET": {"success": false, "error": "Expected payment URL in response"}, "invalidDataHandling": {"success": true, "data": {"error": "Missing required fields", "message": "amount and invoiceNo are required", "required_fields": ["amount", "invoiceNo"]}}}, "environment": {"nodeVersion": "v22.14.0", "platform": "win32", "baseUrl": "http://localhost:3000"}}