import connectedDB from '@/app/config/database'
import SalesMasterData from '@/app/models/SalesMasterData'
// GET /api/dues/:id
export const GET = async (request, { params }) => {
  try {
    await connectedDB()

    const dues = await SalesMasterData.aggregate([
      {
        $match: {
          customerId: params.customerId,
          invoiceStatus: { $in: ['Overdue', 'PartiallyPaid'] },
          aging: { $ne: 0 }, // Filter out entries where aging is 0
        },
      },
      {
        $group: {
          _id: '$aging1', // Group by aging1
          data: { $push: '$$ROOT' }, // Push all matching documents to 'data'
          totalPayableAmount: { $sum: { $toDouble: '$payableAmount' } }, // Sum of payableAmount (convert to number)
          maxDueDays: { $max: '$dueDays' }, // Keep track of max dueDays within the group
        },
      },
      {
        $unwind: '$data', // Unwind the 'data' array for sorting
      },
      {
        $sort: {
          'data.dueDays': -1, // Sort by dueDays in descending order
        },
      },
      {
        $group: {
          _id: '$_id', // Regroup by aging1 after sorting
          data: { $push: '$data' }, // Rebuild the data array
          totalPayableAmount: { $first: '$totalPayableAmount' }, // Retain the total payable amount
          maxDueDays: { $first: '$maxDueDays' }, // Retain maxDueDays
        },
      },
      {
        $project: {
          _id: 0, // Remove MongoDB's internal _id
          aging: '$_id',
          data: 1,
          dueDays: '$maxDueDays', // Include maxDueDays as dueDays
          totalPayableAmount: 1,
        },
      },
      {
        $sort: { dueDays: -1 }, // Sort by dueDays in descending order
      },
    ])

    if (!dues || dues.length === 0) {
      return new Response(JSON.stringify({ results: [] }), { status: 200 })
    }
    return new Response(JSON.stringify({ results: dues }), { status: 200 })
  } catch (error) {
    return new Response(error, { status: 500 })
  }
}
