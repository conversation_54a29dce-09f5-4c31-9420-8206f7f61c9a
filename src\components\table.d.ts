import React from 'react';

export interface TableProps {
  className?: string;
  children?: React.ReactNode;
  [key: string]: any;
}

export interface TableHeadProps {
  className?: string;
  children?: React.ReactNode;
  [key: string]: any;
}

export interface TableBodyProps {
  className?: string;
  children?: React.ReactNode;
  [key: string]: any;
}

export interface TableRowProps {
  className?: string;
  children?: React.ReactNode;
  [key: string]: any;
}

export interface TableHeaderProps {
  className?: string;
  children?: React.ReactNode;
  [key: string]: any;
}

export interface TableCellProps {
  className?: string;
  children?: React.ReactNode;
  [key: string]: any;
}

export function Table(props: TableProps): JSX.Element;
export function TableHead(props: TableHeadProps): JSX.Element;
export function TableBody(props: TableBodyProps): JSX.Element;
export function TableRow(props: TableRowProps): JSX.Element;
export function TableHeader(props: TableHeaderProps): JSX.Element;
export function TableCell(props: TableCellProps): JSX.Element;
