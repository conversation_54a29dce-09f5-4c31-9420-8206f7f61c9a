{"timestamp": "2025-06-14T06:37:29.028Z", "duration": 74, "summary": {"total": 7, "passed": 0, "failed": 7}, "results": {"environmentVariables": {"success": false, "error": "fetch failed"}, "healthCheck": {"success": false, "error": "fetch failed"}, "databaseConnection": {"success": false, "error": "fetch failed"}, "legacyInitiatePayment": {"success": false, "error": "fetch failed"}, "newPaymentSessionAPI": {"success": false, "error": "fetch failed"}, "zohoPaymentGET": {"success": false, "error": "fetch failed"}, "invalidDataHandling": {"success": false, "error": "fetch failed"}}, "environment": {"nodeVersion": "v22.14.0", "platform": "win32", "baseUrl": "http://localhost:3000"}}