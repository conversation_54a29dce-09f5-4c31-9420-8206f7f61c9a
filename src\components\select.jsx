import React from 'react';

export function Select({ className = '', children, ...props }) {
  return (
    <select
      className={`block w-full rounded-md border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm ${className}`}
      {...props}
    >
      {children}
    </select>
  );
}

// Default export for better compatibility
export default Select;