/**
 * Zoho Payment API Integration Tests
 * 
 * This test suite validates the Zoho Payment API integration in the AquaPartner application.
 * It tests both the legacy and new API endpoints for payment processing.
 * 
 * IMPORTANT: These tests use sandbox/test credentials and should not process real payments.
 */

const BASE_URL = process.env.NEXT_PUBLIC_DOMAIN || 'http://localhost:3000';

// Test data
const testPaymentData = {
  amount: 1000.50,
  invoiceNo: 'TEST-INV-001',
  customerId: 'TEST-CUST-001',
  customerName: 'Test Customer',
  customerEmail: '<EMAIL>',
  customerPhone: '+919876543210',
  redirectUrl: 'https://example.com/payment/success',
  referenceId: 'TEST-REF-001'
};

const invalidPaymentData = {
  // Missing required fields
  invoiceNo: 'TEST-INV-002'
};

/**
 * Test Suite 1: Environment Variables
 */
async function testEnvironmentVariables() {
  console.log('\n=== Testing Environment Variables ===');
  
  // Instead of checking process.env directly, use the health check API
  // which already verifies environment variables correctly
  try {
    const baseUrl = process.env.NEXT_PUBLIC_DOMAIN || 'http://localhost:3000';
    const response = await fetch(`${baseUrl}/api/zoho/health`);
    const data = await response.json();
    
    if (data.checks && data.checks.environment) {
      const envStatus = data.checks.environment.status;
      if (envStatus === 'healthy') {
        console.log('✅ All required environment variables are set');
        return { 
          success: true, 
          data: data.checks.environment
        };
      } else {
        console.log('❌ Environment check failed:', data.checks.environment.message);
        return { 
          success: false, 
          error: data.checks.environment.message,
          data: data.checks.environment
        };
      }
    } else {
      console.log('❌ Could not determine environment status');
      return { 
        success: false, 
        error: 'Environment status not available in health check response'
      };
    }
  } catch (error) {
    console.log('❌ Environment variables test failed:', error.message);
    return { 
      success: false, 
      error: error.message
    };
  }
}

/**
 * Test Suite 2: Health Check API
 */
async function testHealthCheck() {
  console.log('\n=== Testing Health Check API ===');
  
  try {
    const response = await fetch(`${BASE_URL}/api/zoho/health`);
    const data = await response.json();
    
    console.log('Health Check Response:', JSON.stringify(data, null, 2));
    
    if (response.ok) {
      console.log('✅ Health check API is accessible');
      return { success: true, data };
    } else {
      console.log('❌ Health check API returned error');
      return { success: false, error: data };
    }
  } catch (error) {
    console.log('❌ Health check API failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Test Suite 3: Legacy Initiate Payment API
 */
async function testLegacyInitiatePayment() {
  console.log('\n=== Testing Legacy Initiate Payment API ===');
  
  // Test 1: Valid payment data
  console.log('\n--- Test 1: Valid Payment Data ---');
  try {
    const response = await fetch(`${BASE_URL}/api/initiatePayment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testPaymentData)
    });
    
    const data = await response.json();
    console.log('Response Status:', response.status);
    console.log('Response Data:', JSON.stringify(data, null, 2));
    
    if (response.ok && data.result === 'success') {
      console.log('✅ Legacy initiate payment API works with valid data');
      return { success: true, data };
    } else {
      console.log('❌ Legacy initiate payment API failed with valid data');
      return { success: false, error: data };
    }
  } catch (error) {
    console.log('❌ Legacy initiate payment API request failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Test Suite 4: Invalid Data Handling
 */
async function testInvalidDataHandling() {
  console.log('\n=== Testing Invalid Data Handling ===');
  
  // Test 1: Missing required fields
  console.log('\n--- Test 1: Missing Required Fields ---');
  try {
    const response = await fetch(`${BASE_URL}/api/initiatePayment`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(invalidPaymentData)
    });
    
    const data = await response.json();
    console.log('Response Status:', response.status);
    console.log('Response Data:', JSON.stringify(data, null, 2));
    
    if (response.status === 400 && data.error) {
      console.log('✅ API correctly handles missing required fields');
      return { success: true, data };
    } else {
      console.log('❌ API did not handle missing required fields correctly');
      return { success: false, error: 'Expected 400 status with error message' };
    }
  } catch (error) {
    console.log('❌ Invalid data test request failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Test Suite 5: New Payment Session API
 */
async function testNewPaymentSessionAPI() {
  console.log('\n=== Testing New Payment Session API ===');
  
  const newPaymentData = {
    amount: testPaymentData.amount,
    currency: 'INR',
    description: 'Test payment for aquaculture products',
    invoice_number: testPaymentData.invoiceNo,
    customer_id: testPaymentData.customerId,
    customer_name: testPaymentData.customerName,
    customer_email: testPaymentData.customerEmail,
    customer_phone: testPaymentData.customerPhone,
    redirect_url: testPaymentData.redirectUrl,
    reference_id: testPaymentData.referenceId,
    meta_data: [
      { key: 'product_type', value: 'aquaculture' },
      { key: 'order_id', value: 'ORD-TEST-001' }
    ]
  };
  
  try {
    const response = await fetch(`${BASE_URL}/api/zoho/payments/create-session`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(newPaymentData)
    });
    
    const data = await response.json();
    console.log('Response Status:', response.status);
    console.log('Response Data:', JSON.stringify(data, null, 2));
    
    if (response.ok && data.success) {
      console.log('✅ New payment session API works correctly');
      return { success: true, data };
    } else {
      console.log('❌ New payment session API failed');
      return { success: false, error: data };
    }
  } catch (error) {
    console.log('❌ New payment session API request failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Test Suite 6: Zoho Payment GET API
 */
async function testZohoPaymentGET() {
  console.log('\n=== Testing Zoho Payment GET API ===');
  
  try {
    const response = await fetch(`${BASE_URL}/api/zohoPayment?amount=${testPaymentData.amount}&invoice=${testPaymentData.invoiceNo}`);
    
    console.log('Response Status:', response.status);
    
    const data = await response.json();
    console.log('Response Data:', JSON.stringify(data, null, 2));
    
    if (response.ok && data.success && data.payment_url) {
      console.log('✅ Zoho Payment GET API returns valid payment URL:', data.payment_url);
      return { success: true, data };
    } else {
      console.log('❌ Zoho Payment GET API did not return a valid payment URL');
      return { success: false, error: 'Expected payment URL in response' };
    }
  } catch (error) {
    console.log('❌ Zoho Payment GET API request failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Test Suite 7: Database Connection Test
 */
async function testDatabaseConnection() {
  console.log('\n=== Testing Database Connection ===');
  
  try {
    // Test by calling an API that requires database connection
    const response = await fetch(`${BASE_URL}/api/zoho/health`);
    const data = await response.json();
    
    if (data.checks && data.checks.database) {
      const dbStatus = data.checks.database.status;
      if (dbStatus === 'healthy') {
        console.log('✅ Database connection is healthy');
        return { success: true, status: dbStatus };
      } else {
        console.log('❌ Database connection is unhealthy');
        return { success: false, status: dbStatus };
      }
    } else {
      console.log('❌ Could not determine database status');
      return { success: false, error: 'Database status not available' };
    }
  } catch (error) {
    console.log('❌ Database connection test failed:', error.message);
    return { success: false, error: error.message };
  }
}

/**
 * Main Test Runner
 */
async function runAllTests() {
  console.log('🚀 Starting Zoho Payment API Integration Tests');
  console.log('================================================');
  
  const testResults = {
    environmentVariables: await testEnvironmentVariables(),
    healthCheck: await testHealthCheck(),
    databaseConnection: await testDatabaseConnection(),
    legacyInitiatePayment: await testLegacyInitiatePayment(),
    invalidDataHandling: await testInvalidDataHandling(),
    newPaymentSessionAPI: await testNewPaymentSessionAPI(),
    zohoPaymentGET: await testZohoPaymentGET()
  };
  
  // Summary
  console.log('\n================================================');
  console.log('📊 TEST SUMMARY');
  console.log('================================================');
  
  let totalTests = 0;
  let passedTests = 0;
  
  for (const [testName, result] of Object.entries(testResults)) {
    totalTests++;
    const status = result.success ? '✅ PASSED' : '❌ FAILED';
    console.log(`${testName}: ${status}`);
    if (result.success) passedTests++;
  }
  
  console.log(`\nOverall: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Zoho Payment API integration is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please check the configuration and try again.');
  }
  
  return testResults;
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runAllTests,
    testEnvironmentVariables,
    testHealthCheck,
    testLegacyInitiatePayment,
    testInvalidDataHandling,
    testNewPaymentSessionAPI,
    testZohoPaymentGET,
    testDatabaseConnection
  };
}

// Run tests if this file is executed directly
if (typeof window === 'undefined' && require.main === module) {
  runAllTests().catch(console.error);
}
