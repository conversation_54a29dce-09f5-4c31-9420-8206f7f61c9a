import { Providers } from '@/customComponents/providers'
import '@/styles/tailwind.css'
import type { Metadata } from 'next'
import type React from 'react'
export const metadata: Metadata = {
  title: {
    template: 'Aquapartner',
    default: 'Aquapartner App',
  },
  description: 'One stop solution for Aqua culture needs.',
}

export default function LoginLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" className="h-full bg-white">
      <head>
        <link rel="preconnect" href="https://rsms.me/" />
        <link rel="stylesheet" href="https://rsms.me/inter/inter.css" />
        <link rel="icon" href="/favicon.png" />
      </head>
      <body className="h-full">
        <Providers>{children}</Providers>
      </body>
    </html>
  )
}
