import path from 'path'

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  productionBrowserSourceMaps: false,
  webpack: (config, { dev }) => {
    if (dev) {
      config.devtool = false
    }

    // Optimize webpack memory usage for production builds
    if (!dev && config.cache) {
      config.cache = {
        type: 'memory',
      }
    }

    // Enhanced module resolution for Azure Static Web Apps CI/CD (Oryx build system)
    // This addresses the specific issue with explicit file extensions + path aliases in Linux environments

    // 1. Configure extension resolution with fallbacks
    config.resolve.extensionAlias = {
      '.js': ['.js', '.jsx', '.ts', '.tsx'],
      '.jsx': ['.jsx', '.js'],
      '.ts': ['.ts', '.tsx'],
      '.tsx': ['.tsx', '.ts']
    }

    // 2. Ensure proper extension order for resolution
    config.resolve.extensions = ['.tsx', '.ts', '.jsx', '.js', '.json']

    // 3. Configure module resolution for Linux CI/CD environments
    config.resolve.symlinks = false
    config.resolve.cacheWithContext = false

    // 4. Add explicit alias resolution for problematic paths
    config.resolve.alias = {
      ...config.resolve.alias,
      '@': path.resolve(process.cwd(), 'src'),
      '@/components': path.resolve(process.cwd(), 'src/components'),
      '@/customComponents': path.resolve(process.cwd(), 'src/customComponents'),
      '@/utils': path.resolve(process.cwd(), 'src/utils'),
      '@/app': path.resolve(process.cwd(), 'src/app')
    }

    // 5. Additional optimization for CI/CD environments
    // Force consistent casing behavior across platforms
    if (!dev) {
      config.optimization = {
        ...config.optimization,
        moduleIds: 'deterministic'
      }
    }

    return config
  },
  // Ignore TypeScript errors during build
  typescript: {
    ignoreBuildErrors: true,
  },
  // Ignore ESLint errors during build
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Add experimental configuration for memory optimizations
  experimental: {
    webpackMemoryOptimizations: true,
  },
}

export default nextConfig
