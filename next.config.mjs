/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  productionBrowserSourceMaps: false,
  webpack: (config, { dev }) => {
    if (dev) {
      config.devtool = false
    }

    // Optimize webpack memory usage for production builds
    if (!dev && config.cache) {
      config.cache = {
        type: 'memory',
      }
    }

    // Improve module resolution for CI/CD environments
    config.resolve = {
      ...config.resolve,
      extensions: ['.tsx', '.ts', '.jsx', '.js', '.json'],
      alias: {
        ...config.resolve.alias,
        '@': require('path').resolve(__dirname, 'src'),
      },
      // Ensure case-sensitive module resolution
      caseSensitive: true,
      // Prefer explicit extensions
      enforceExtension: false,
      // Fallback for module resolution
      fallback: {
        ...config.resolve.fallback,
      }
    }

    return config
  },
  // Ignore TypeScript errors during build
  typescript: {
    ignoreBuildErrors: true,
  },
  // Ignore ESLint errors during build
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Add experimental configuration for Suspense and memory optimizations
  experimental: {
    missingSuspenseWithCSRBailout: false,
    webpackMemoryOptimizations: true,
  },
}

export default nextConfig
