/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  productionBrowserSourceMaps: false,
  webpack: (config, { dev }) => {
    if (dev) {
      config.devtool = false
    }
    
    // Optimize webpack memory usage for production builds
    if (!dev && config.cache) {
      config.cache = {
        type: 'memory',
      }
    }
    
    return config
  },
  // Ignore TypeScript errors during build
  typescript: {
    ignoreBuildErrors: true,
  },
  // Ignore ESLint errors during build
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Add experimental configuration for Suspense and memory optimizations
  experimental: {
    missingSuspenseWithCSRBailout: false,
    webpackMemoryOptimizations: true,
  },
}

export default nextConfig
