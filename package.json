{"name": "aqua<PERSON><PERSON><PERSON>", "version": "0.0.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:debug": "NEXT_DEBUG=1 next build", "start": "next start", "lint": "next lint", "check-modules": "node -e \"console.log('Checking module resolution...'); try { require('./src/components/divider.jsx'); console.log('✓ divider.jsx found'); } catch(e) { console.log('✗ divider.jsx not found:', e.message); } try { require('./src/components/select.jsx'); console.log('✓ select.jsx found'); } catch(e) { console.log('✗ select.jsx not found:', e.message); } try { require('./src/components/table.jsx'); console.log('✓ table.jsx found'); } catch(e) { console.log('✗ table.jsx not found:', e.message); } try { require('./src/customComponents/common/stat.jsx'); console.log('✓ stat.jsx found'); } catch(e) { console.log('✗ stat.jsx not found:', e.message); }\""}, "dependencies": {"@headlessui/react": "^2.1.1", "@heroicons/react": "^2.1.3", "apexcharts": "^3.53.0", "axios": "^1.7.8", "chart.js": "^4.4.3", "chart.js-plugin-labels-hb": "^5.0.2-beta", "chartjs-plugin-datalabels": "^2.2.0", "clsx": "^2.1.1", "firebase": "^11.2.0", "firebase-admin": "^13.2.0", "flowbite": "^2.5.1", "framer-motion": "^11.2.6", "html2canvas": "^1.4.1", "jotai": "^2.9.3", "leaflet": "^1.9.4", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "mongoose": "^8.5.2", "next": "^15.3.3", "react": "^18", "react-apexcharts": "^1.4.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18", "react-gtag": "^0.0.0", "react-leaflet": "^4.2.1", "react-multi-carousel": "^2.8.5", "react-phone-input-2": "^2.15.1"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@types/lodash": "^4.17.7", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.19", "eslint": "^8", "eslint-config-next": "14.2.3", "postcss": "^8", "prettier": "^3.3.2", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-tailwindcss": "^0.6.5", "tailwindcss": "^3.4.4", "typescript": "^5"}}