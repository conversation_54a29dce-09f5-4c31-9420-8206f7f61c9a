'use client'
import { PriceCard } from '@/customComponents/PriceCard'
import { priceListAtom } from '@/customComponents/providers'
import { priceList } from '@/utils/price-list'
import { Dialog, DialogPanel, DialogTitle } from '@headlessui/react'
import { useAtom } from 'jotai'
import { useState } from 'react'

const PriceListPage = () => {
  const [priceListValue, setPriceListValue] = useAtom(priceListAtom)
  const [isDialogOpen, setDialogOpen] = useState(false)

  const handleChangeState = () => {
    setDialogOpen((prev) => !prev)
  }

  const handleStateChange = (state: any) => {
    setPriceListValue(state)
    setDialogOpen((prev) => !prev)
  }

  return (
    <>
      <div className="mt-2 pb-6 align-baseline sm:flex sm:justify-between">
        <div className="flex flex-col sm:flex-row">
          <p className="text-2xl font-bold">Prices List</p>
        </div>
      </div>

      <div className="flex flex-col justify-start">
        <div className="flex flex-row justify-between">
          <div className="flex flex-row">
            <img
              src="/images/location-icon.svg" // Replace with your icon URL
              alt="location Icon"
              className="mr-2 h-6 w-6"
            />
            <p className="font-bold capitalize">{priceListValue?.state || 'Select State'}</p>
          </div>
          <button className="flex text-base font-semibold text-blue-600 hover:underline" onClick={handleChangeState}>
            Change
          </button>
        </div>
        {priceListValue && priceListValue.prices && (
          <PriceCard
            title={'Vennamei'}
            date={priceListValue.date}
            isSharable={false}
            priceData={priceListValue.prices}
          />
        )}
      </div>

      {isDialogOpen && (
        <Dialog open={isDialogOpen} onClose={() => setDialogOpen(false)} className="relative z-50">
          <div className="fixed inset-0 bg-black/30" aria-hidden="true" />

          <div className="fixed inset-0 flex items-center justify-center p-4">
            <DialogPanel className="mx-auto max-w-sm rounded bg-white p-6">
              <DialogTitle className="mb-4 text-lg font-medium">Select State</DialogTitle>

              <div className="space-y-2">
                {priceList && priceList.map((state) => (
                  <label key={state.state} className="flex cursor-pointer items-center space-x-2">
                    <input
                      type="radio"
                      name="state"
                      value={state.state}
                      checked={priceListValue?.state === state.state}
                      onChange={() => handleStateChange(state)}
                      className="form-radio text-blue-600"
                    />
                   <span>{state.state.charAt(0).toUpperCase() + state.state.slice(1)}</span>
                  </label>
                ))}
              </div>
            </DialogPanel>
          </div>
        </Dialog>
      )}
    </>
  )
}

export default PriceListPage
