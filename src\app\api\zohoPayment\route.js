import { NextResponse } from 'next/server';
import zohoPaymentService from '@/app/lib/zohoPaymentService';

/**
 * GET /api/zohoPayment
 * Legacy endpoint that returns HTML with client-side redirect
 */
export async function GET(request) {
  try {
    const url = new URL(request.url);
    const amount = url.searchParams.get('amount');
    const invoice = url.searchParams.get('invoice');
    
    if (!amount || !invoice) {
      return NextResponse.json(
        { 
          error: 'Missing required parameters', 
          message: 'amount and invoice are required' 
        }, 
        { status: 400 }
      );
    }
    
    // Create payment session
    const paymentData = {
      amount: parseFloat(amount),
      invoice_number: invoice,
      description: `Payment for Invoice ${invoice}`,
      meta_data: [
        { key: 'source', value: 'get_api' },
        { key: 'invoice_number', value: invoice }
      ]
    };
    
    const result = await zohoPaymentService.createPaymentSession(paymentData);
    
    // Get the payment URL
    const paymentUrl = `https://payments.zoho.in/checkout/${result.payments_session_id}`;
    
    // Return HTML with client-side redirect
    const html = `
      <!DOCTYPE html>
      <html>
        <head>
          <meta http-equiv="refresh" content="0;url=${paymentUrl}">
          <title>Redirecting to Zoho Payment</title>
        </head>
        <body>
          <p>Redirecting to payment page...</p>
          <p>If you are not redirected, <a href="${paymentUrl}">click here</a>.</p>
          <script>window.location.href = "${paymentUrl}";</script>
        </body>
      </html>
    `;
    
    return new Response(html, {
      status: 200,
      headers: {
        'Content-Type': 'text/html'
      }
    });
  } catch (error) {
    console.error('Zoho Payment GET API error:', error);
    return NextResponse.json(
      { 
        error: 'Payment session creation failed', 
        message: error.message 
      }, 
      { status: 500 }
    );
  }
}
