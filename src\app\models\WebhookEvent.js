import { Schema, model, models } from 'mongoose'

const WebhookEventSchema = new Schema(
  {
    event_id: {
      type: String,
      required: true,
      // Remove the index: true since we're using schema.index() below
    },
    payment_session_id: String,
    event_type: String,
    event_data: Object,
    webhook_received_at: {
      type: Date,
      default: Date.now
    },
    processed: {
      type: Boolean,
      default: false
    },
    processed_at: Date,
    processing_attempts: {
      type: Number,
      default: 0
    },
    processing_errors: [String]
  },
  {
    timestamps: true
  }
)

// Indexes for efficient queries - keep only these, remove any duplicate index declarations
WebhookEventSchema.index({ event_id: 1 }, { unique: true })
WebhookEventSchema.index({ payment_session_id: 1 })
WebhookEventSchema.index({ event_type: 1 })
WebhookEventSchema.index({ processed: 1 })
WebhookEventSchema.index({ webhook_received_at: 1 })

// Methods
WebhookEventSchema.methods.markAsProcessed = function() {
  this.processed = true
  this.processed_at = new Date()
  return this.save()
}

WebhookEventSchema.methods.incrementProcessingAttempts = function() {
  this.processing_attempts += 1
  return this.save()
}

const WebhookEvent = models.WebhookEvent || model('WebhookEvent', WebhookEventSchema)

export default WebhookEvent
