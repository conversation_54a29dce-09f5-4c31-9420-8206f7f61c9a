import React from 'react';

export function Table({ className = '', children, ...props }) {
  return (
    <div className="overflow-x-auto">
      <table
        className={`min-w-full divide-y divide-gray-300 ${className}`}
        {...props}
      >
        {children}
      </table>
    </div>
  );
}

export function TableHead({ className = '', children, ...props }) {
  return (
    <thead className={`bg-gray-50 ${className}`} {...props}>
      {children}
    </thead>
  );
}

export function TableBody({ className = '', children, ...props }) {
  return (
    <tbody
      className={`divide-y divide-gray-200 bg-white ${className}`}
      {...props}
    >
      {children}
    </tbody>
  );
}

export function TableRow({ className = '', children, ...props }) {
  return (
    <tr className={className} {...props}>
      {children}
    </tr>
  );
}

export function TableHeader({ className = '', children, ...props }) {
  return (
    <th
      scope="col"
      className={`py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 ${className}`}
      {...props}
    >
      {children}
    </th>
  );
}

export function TableCell({ className = '', children, ...props }) {
  return (
    <td
      className={`whitespace-nowrap py-4 pl-4 pr-3 text-sm text-gray-500 sm:pl-6 ${className}`}
      {...props}
    >
      {children}
    </td>
  );
}

// Default exports for better compatibility
export default Table;