import React from 'react';

export function Stat({ title, value, description, icon, className, ...props }) {
  return (
    <div
      className={`overflow-hidden rounded-lg bg-white px-4 py-5 shadow sm:p-6 ${className || ''}`}
      {...props}
    >
      <div className="flex items-center">
        {icon && (
          <div className="flex-shrink-0">
            <div className="h-10 w-10 rounded-md bg-indigo-500 flex items-center justify-center">
              {icon}
            </div>
          </div>
        )}
        <div className={icon ? "ml-5 w-0 flex-1" : "w-full"}>
          <dt className="truncate text-sm font-medium text-gray-500">{title}</dt>
          <dd className="mt-1 text-3xl font-semibold tracking-tight text-gray-900">
            {value}
          </dd>
          {description && (
            <p className="mt-1 text-sm text-gray-500">{description}</p>
          )}
        </div>
      </div>
    </div>
  );
}

// Default export for better compatibility
export default Stat;
